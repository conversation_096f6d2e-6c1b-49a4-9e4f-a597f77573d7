//package com.example.web_project.security;
//
//import com.example.web_project.common.Role;
//import com.example.web_project.entity.User;
//import com.example.web_project.repository.UserRepository;
//import jakarta.annotation.PostConstruct;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.stereotype.Component;
//
//@Component
//public class DataInitializer {
//
//    @Autowired
//    private UserRepository userRepository;
//
//    @Autowired
//    private PasswordEncoder passwordEncoder;
//
//    @PostConstruct
//    public void initAdmin() {
//        if (userRepository.findByUsername("admin").isEmpty()) {
//            User admin = new User();
//            admin.setUsername("admin");
//            admin.setPassword(passwordEncoder.encode("123456"));
//            admin.setRole(Role.ROLE_WRITER);
//            userRepository.save(admin);
//            System.out.println("Admin user created");
//        }
//    }
//}
//
