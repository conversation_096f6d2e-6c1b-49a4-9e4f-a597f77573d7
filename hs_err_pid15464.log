#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffc12653004, pid=15464, tid=23260
#
# JRE version: OpenJDK Runtime Environment (24.0+36) (build 24+36-3646)
# Java VM: OpenJDK 64-Bit Server VM (24+36-3646, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# C  [libasyncProfiler.dll+0x93004]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:57776,suspend=y,server=n -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\WebProjectApplication_2025_07_18_222324.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\WebProjectApplication_2025_07_18_222324.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 com.example.web_project.WebProjectApplication

Host: 11th Gen Intel(R) Core(TM) i7-11800H @ 2.30GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Fri Jul 18 22:25:31 2025 SE Asia Standard Time elapsed time: 126.785421 seconds (0d 0h 2m 6s)

---------------  T H R E A D  ---------------

Current thread (0x00000158e668ddd0):  JavaThread "Attach Listener" daemon [_thread_in_native, id=23260, stack(0x0000003efdf00000,0x0000003efe000000) (1024K)]

Stack: [0x0000003efdf00000,0x0000003efe000000],  sp=0x0000003efdffee00,  free space=1019k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libasyncProfiler.dll+0x93004]  (no source info available)
C  [libasyncProfiler.dll+0x934db]  (no source info available)
C  [libasyncProfiler.dll+0x92134]  (no source info available)
C  [libasyncProfiler.dll+0x929aa]  (no source info available)
C  [libasyncProfiler.dll+0x8d561]  (no source info available)
C  [libasyncProfiler.dll+0x7ef21]  (no source info available)
C  [libasyncProfiler.dll+0x7dc38]  (no source info available)
C  [libasyncProfiler.dll+0x7db20]  (no source info available)
C  [libasyncProfiler.dll+0x84af9]  (no source info available)
V  [jvm.dll+0x57f1b7]  (no source info available)
V  [jvm.dll+0x57ff2c]  (no source info available)
V  [jvm.dll+0x14c9b7]  (no source info available)
V  [jvm.dll+0x14df81]  (no source info available)
V  [jvm.dll+0x42f566]  (no source info available)
V  [jvm.dll+0x87d8fb]  (no source info available)
V  [jvm.dll+0x72efe5]  (no source info available)
C  [ucrtbase.dll+0x37b0]  (no source info available)
C  [KERNEL32.DLL+0x2e8d7]  (no source info available)
C  [ntdll.dll+0x3c34c]  (no source info available)


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0xffffffffffffffff


Registers:
RAX=0x0000000000000010, RBX=0x0000000000000000, RCX=0xffffffffffffffff, RDX=0x007ffc26d22ff010
RSP=0x0000003efdffee00, RBP=0x00000158c7b98d50, RSI=0x0000003efdffef70, RDI=0x00000158c7b98d50
R8 =0x0000000000000042, R9 =0x0000000704e00000, R10=0x00007ffc26f99fc0, R11=0x000000000000009e
R12=0x0000003efdffef70, R13=0x00000158e4489e30, R14=0x00000158e668e198, R15=0x00000158e668e198
RIP=0x00007ffc12653004, EFLAGS=0x0000000000010202

XMM[0]=0x0000000000000000 0x0000000000000000
XMM[1]=0x0000000000000000 0x0000000000000000
XMM[2]=0x0000000000000000 0x0000000000000000
XMM[3]=0x0000000000000000 0x0000000000000000
XMM[4]=0x0000000000000000 0x0000000000000000
XMM[5]=0x0000000000000000 0x0000000000000000
XMM[6]=0x0000000000000000 0x0000000000000000
XMM[7]=0x0000000000000000 0x0000000000000000
XMM[8]=0x0000000000000000 0x0000000000000000
XMM[9]=0x0000000000000000 0x0000000000000000
XMM[10]=0x0000000000000000 0x0000000000000000
XMM[11]=0x0000000000000000 0x0000000000000000
XMM[12]=0x0000000000000000 0x0000000000000000
XMM[13]=0x0000000000000000 0x0000000000000000
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001fa0


Register to memory mapping:

RAX=0x0000000000000010 is an unknown value
RBX=0x0 is null
RCX=0xffffffffffffffff is an unknown value
RDX=0x007ffc26d22ff010 is an unknown value
RSP=0x0000003efdffee00 is pointing into the stack for thread: 0x00000158e668ddd0
RBP=0x00000158c7b98d50 points into unknown readable memory: 0x0000000704e00000 | 00 00 e0 04 07 00 00 00
RSI=0x0000003efdffef70 is pointing into the stack for thread: 0x00000158e668ddd0
RDI=0x00000158c7b98d50 points into unknown readable memory: 0x0000000704e00000 | 00 00 e0 04 07 00 00 00
R8 =0x0000000000000042 is an unknown value
R9 =0x0000000704e00000 is an oop: java.lang.ref.Reference$ReferenceHandler 
{0x0000000704e00000} - klass: 'java/lang/ref/Reference$ReferenceHandler' - flags: 

 - ---- fields (total size 14 words):
 - 'threadLocalRandomProbe' 'I' @12  0 (0x00000000)
 - private volatile 'eetop' 'J' @16  1481334369040 (0x00000158e668ab10)
 - private final 'tid' 'J' @24  15 (0x000000000000000f)
 - 'threadLocalRandomSeed' 'J' @32  0 (0x0000000000000000)
 - injected 'jvmti_thread_state' 'J' @40  1480709316144 (0x00000158c1271e30)
 - 'threadLocalRandomSecondarySeed' 'I' @48  0 (0x00000000)
 - injected 'jvmti_VTMS_transition_disable_count' 'I' @52  0 (0x00000000)
 - injected 'jfr_epoch' 'S' @56  0 (0x0000)
 - volatile 'interrupted' 'Z' @58  false (0x00)
 - injected 'jvmti_is_in_VTMS_transition' 'Z' @59  false (0x00)
 - private volatile 'name' 'Ljava/lang/String;' @60  "Reference Handler"{0x0000000705418fc0} (0xe0a831f8)
 - private volatile 'contextClassLoader' 'Ljava/lang/ClassLoader;' @64  null (0x00000000)
 - private final 'holder' 'Ljava/lang/Thread$FieldHolder;' @68  a 'java/lang/Thread$FieldHolder'{0x00000007057266d8} (0xe0ae4cdb)
 - 'threadLocals' 'Ljava/lang/ThreadLocal$ThreadLocalMap;' @72  null (0x00000000)
 - 'inheritableThreadLocals' 'Ljava/lang/ThreadLocal$ThreadLocalMap;' @76  null (0x00000000)
 - private 'scopedValueBindings' 'Ljava/lang/Object;' @80  a 'java/lang/Class'{0x0000000704e037f0} = 'java/lang/Thread' (0xe09c06fe)
 - final 'interruptLock' 'Ljava/lang/Object;' @84  a 'java/lang/Object'{0x00000007057266c8} (0xe0ae4cd9)
 - private volatile 'parkBlocker' 'Ljava/lang/Object;' @88  null (0x00000000)
 - private 'nioBlocker' 'Lsun/nio/ch/Interruptible;' @92  null (0x00000000)
 - private 'cont' 'Ljdk/internal/vm/Continuation;' @96  null (0x00000000)
 - private volatile 'uncaughtExceptionHandler' 'Ljava/lang/Thread$UncaughtExceptionHandler;' @100  null (0x00000000)
 - private 'container' 'Ljdk/internal/vm/ThreadContainer;' @104  null (0x00000000)
 - private volatile 'headStackableScopes' 'Ljdk/internal/vm/StackableScope;' @108  null (0x00000000)
R10=0x00007ffc26f99fc0 jvm.dll
R11=0x000000000000009e is an unknown value
R12=0x0000003efdffef70 is pointing into the stack for thread: 0x00000158e668ddd0
R13=0x00000158e4489e30 points into unknown readable memory: 0x00007ffc26f0a510 | 10 a5 f0 26 fc 7f 00 00
R14=0x00000158e668e198 points into unknown readable memory: 0x00007ffc26f017b0 | b0 17 f0 26 fc 7f 00 00
R15=0x00000158e668e198 points into unknown readable memory: 0x00007ffc26f017b0 | b0 17 f0 26 fc 7f 00 00

Top of Stack: (sp=0x0000003efdffee00)
0x0000003efdffee00:   0000003efdffef70 00000158c7b98d50   p...>...P...X...
0x0000003efdffee10:   0000000000000000 00000158e668e7a0   ..........h.X...
0x0000003efdffee20:   000000000000003c 00007ffc126534db   <........4e.....
0x0000003efdffee30:   0000000000000000 00000158e668e198   ..........h.X...
0x0000003efdffee40:   0000003efdffef01 0000000000000000   ....>...........
0x0000003efdffee50:   00000158c6e6ede0 00007ffc12652134   ....X...4!e.....
0x0000003efdffee60:   0000000000000000 000000000000003c   ........<.......
0x0000003efdffee70:   0000000000000000 000000000000003c   ........<.......
0x0000003efdffee80:   0000001d00000002 00000158cac4d3d0   ............X...
0x0000003efdffee90:   00000158c6e6ede0 00000158c6e6eed0   ....X.......X...
0x0000003efdffeea0:   00000158c6e6eedc 0000000000003dc4   ....X....=......
0x0000003efdffeeb0:   0000000000000000 0000000000000000   ................
0x0000003efdffeec0:   0000000000000000 00007ffc267658e4   .........Xv&....
0x0000003efdffeed0:   00007ffc127aae00 0000000000003c68   ..z.....h<......
0x0000003efdffeee0:   00007ffc127aae00 00000000ffffffff   ..z.............
0x0000003efdffeef0:   0000000000000000 00007ffc126529aa   .........)e.....
0x0000003efdffef00:   00000158e668e198 0000000000000000   ..h.X...........
0x0000003efdffef10:   0000003efdffef79 00000158e4489e30   y...>...0.H.X...
0x0000003efdffef20:   00007ffc00005d98 0000000000000000   .]..............
0x0000003efdffef30:   0000000000000000 00007ffcf06ff559   ........Y.o.....
0x0000003efdffef40:   00000158e668e198 00007ffc127aae00   ..h.X.....z.....
0x0000003efdffef50:   0000000000000000 00007ffc126ebc53   ........S.n.....
0x0000003efdffef60:   0000000000000000 00000158e668e198   ..........h.X...
0x0000003efdffef70:   00000158c6929300 00000158c6929a80   ....X.......X...
0x0000003efdffef80:   00000158c6929a80 00007ffc127aae00   ....X.....z.....
0x0000003efdffef90:   00000158e697fe68 0000003efdfff108   h...X.......>...
0x0000003efdffefa0:   0000003efdfff240 0000000000008840   @...>...@.......
0x0000003efdffefb0:   0000003efdfff420 00007ffc127aae50    ...>...P.z.....
0x0000003efdffefc0:   0000000000989680 0000000000989680   ................
0x0000003efdffefd0:   0000003efdfff0e0 00007ffc1264d561   ....>...a.d.....
0x0000003efdffefe0:   00007ffc127aae00 00007ffc127aae58   ..z.....X.z.....
0x0000003efdffeff0:   00007ffc00000000 003ed58100000001   ..............>.

Instructions: (pc=0x00007ffc12653004)
  0x00007ffc12652f04:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12652f14:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12652f24:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12652f34:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12652f44:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12652f54:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12652f64:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12652f74:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12652f84:   cc cc cc cc cc cc cc cc cc cc cc cc 40 57 48 83
  0x00007ffc12652f94:   ec 20 48 8b f9 48 8b 0d 30 80 15 00 48 85 c9 74
  0x00007ffc12652fa4:   20 48 8b 01 48 8d 54 24 38 41 b8 06 00 01 00 4c
  0x00007ffc12652fb4:   8b 48 30 41 ff d1 85 c0 75 07 48 8b 4c 24 38 eb
  0x00007ffc12652fc4:   02 33 c9 4c 8b 05 0a 81 15 00 48 89 5c 24 30 4d
  0x00007ffc12652fd4:   85 c0 74 4a 48 8b 01 48 8b d7 4c 8b 88 28 03 00
  0x00007ffc12652fe4:   00 41 ff d1 48 85 c0 74 35 48 63 0d b8 3d 15 00
  0x00007ffc12652ff4:   48 8b 14 01 48 85 d2 74 15 48 63 05 bc 3d 15 00
=>0x00007ffc12653004:   8b 04 10 48 8b 5c 24 30 48 83 c4 20 5f c3 b8 ff
  0x00007ffc12653014:   ff ff ff 48 8b 5c 24 30 48 83 c4 20 5f c3 80 3d
  0x00007ffc12653024:   bb 7f 15 00 00 bb ff ff ff ff 74 20 4c 8b 0d 51
  0x00007ffc12653034:   7c 15 00 4c 8d 44 24 38 48 8b 0d 35 7c 15 00 48
  0x00007ffc12653044:   8b d7 41 ff d1 85 c0 0f 44 5c 24 38 8b c3 48 8b
  0x00007ffc12653054:   5c 24 30 48 83 c4 20 5f c3 cc cc cc cc cc cc cc
  0x00007ffc12653064:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12653074:   cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc
  0x00007ffc12653084:   cc cc cc cc cc cc cc cc cc cc cc cc 48 89 54 24
  0x00007ffc12653094:   10 53 55 56 57 41 56 41 57 48 83 ec 28 48 8b f2
  0x00007ffc126530a4:   4c 8b f1 48 89 4c 24 78 45 33 ff 44 89 7c 24 68
  0x00007ffc126530b4:   c7 44 24 60 01 00 00 00 48 8d 4c 24 68 e8 69 3c
  0x00007ffc126530c4:   f7 ff 8b d8 48 8d 4c 24 60 e8 5d 3c f7 ff 8b e8
  0x00007ffc126530d4:   49 8b ce e8 33 f3 f6 ff 48 8b f8 90 8b c3 f0 0f
  0x00007ffc126530e4:   b1 2f 74 04 f3 90 eb f4 4c 89 3e 4c 89 7e 08 4c
  0x00007ffc126530f4:   89 7e 10 49 8b 6e 08 4d 8b 76 10 49 3b ee 0f 84


Stack slot to memory mapping:

stack at sp + 0 slots: 0x0000003efdffef70 is pointing into the stack for thread: 0x00000158e668ddd0
stack at sp + 1 slots: 0x00000158c7b98d50 points into unknown readable memory: 0x0000000704e00000 | 00 00 e0 04 07 00 00 00
stack at sp + 2 slots: 0x0 is null
stack at sp + 3 slots: 0x00000158e668e7a0 points into unknown readable memory: 0x6e616c2e6176616a | 6a 61 76 61 2e 6c 61 6e
stack at sp + 4 slots: 0x000000000000003c is an unknown value
stack at sp + 5 slots: 0x00007ffc126534db libasyncProfiler.dll
stack at sp + 6 slots: 0x0 is null
stack at sp + 7 slots: 0x00000158e668e198 points into unknown readable memory: 0x00007ffc26f017b0 | b0 17 f0 26 fc 7f 00 00

Lock stack of current Java thread (top to bottom):


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000158cb82d3e0, length=35, elements={
0x00000158e668ab10, 0x00000158e668b350, 0x00000158c1122220, 0x00000158e668ddd0,
0x00000158c1122fb0, 0x00000158c11236f0, 0x00000158c1130370, 0x00000158c126a9f0,
0x00000158c13156a0, 0x00000158c12cf000, 0x00000158c12cf750, 0x00000158c13fc860,
0x00000158c66e4b50, 0x00000158c7696060, 0x00000158c885c280, 0x00000158c885bb30,
0x00000158c885c9d0, 0x00000158c885b3e0, 0x00000158c9d2b740, 0x00000158cae40060,
0x00000158cae42c40, 0x00000158cae41650, 0x00000158cae3f910, 0x00000158cae424f0,
0x00000158ca45a5b0, 0x00000158ca458870, 0x00000158cb77a510, 0x00000158cb7771e0,
0x00000158cb778f20, 0x00000158cb778080, 0x00000158cb779dc0, 0x00000158cb77ac60,
0x00000158c117dc90, 0x00000158cb435710, 0x00000158cb439b20
}

Java Threads: ( => current thread )
  0x00000158e668ab10 JavaThread "Reference Handler"          daemon [_thread_blocked, id=21140, stack(0x0000003efdc00000,0x0000003efdd00000) (1024K)]
  0x00000158e668b350 JavaThread "Finalizer"                  daemon [_thread_blocked, id=6432, stack(0x0000003efdd00000,0x0000003efde00000) (1024K)]
  0x00000158c1122220 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=14760, stack(0x0000003efde00000,0x0000003efdf00000) (1024K)]
=>0x00000158e668ddd0 JavaThread "Attach Listener"            daemon [_thread_in_native, id=23260, stack(0x0000003efdf00000,0x0000003efe000000) (1024K)]
  0x00000158c1122fb0 JavaThread "Service Thread"             daemon [_thread_blocked, id=28468, stack(0x0000003efe000000,0x0000003efe100000) (1024K)]
  0x00000158c11236f0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=2692, stack(0x0000003efe100000,0x0000003efe200000) (1024K)]
  0x00000158c1130370 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=28664, stack(0x0000003efe200000,0x0000003efe300000) (1024K)]
  0x00000158c126a9f0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=22724, stack(0x0000003efe400000,0x0000003efe500000) (1024K)]
  0x00000158c13156a0 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=16200, stack(0x0000003efe700000,0x0000003efe800000) (1024K)]
  0x00000158c12cf000 JavaThread "JDWP Event Helper Thread"   daemon [_thread_blocked, id=6236, stack(0x0000003efe800000,0x0000003efe900000) (1024K)]
  0x00000158c12cf750 JavaThread "JDWP Command Reader"        daemon [_thread_in_native, id=4532, stack(0x0000003efe900000,0x0000003efea00000) (1024K)]
  0x00000158c13fc860 JavaThread "Notification Thread"        daemon [_thread_blocked, id=12080, stack(0x0000003efea00000,0x0000003efeb00000) (1024K)]
  0x00000158c66e4b50 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=1304, stack(0x0000003efeb00000,0x0000003efec00000) (1024K)]
  0x00000158c7696060 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=4076, stack(0x0000003efff00000,0x0000003f00000000) (1024K)]
  0x00000158c885c280 JavaThread "Catalina-utility-1"                [_thread_blocked, id=23048, stack(0x0000003e80000000,0x0000003e80100000) (1024K)]
  0x00000158c885bb30 JavaThread "Catalina-utility-2"                [_thread_blocked, id=19280, stack(0x0000003e80100000,0x0000003e80200000) (1024K)]
  0x00000158c885c9d0 JavaThread "container-0"                       [_thread_blocked, id=21636, stack(0x0000003e80200000,0x0000003e80300000) (1024K)]
  0x00000158c885b3e0 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=6592, stack(0x0000003e80300000,0x0000003e80400000) (1024K)]
  0x00000158c9d2b740 JavaThread "HikariPool-1:housekeeper"   daemon [_thread_blocked, id=28172, stack(0x0000003e80700000,0x0000003e80800000) (1024K)]
  0x00000158cae40060 JavaThread "http-nio-8080-exec-1"       daemon [_thread_blocked, id=25488, stack(0x0000003e80800000,0x0000003e80900000) (1024K)]
  0x00000158cae42c40 JavaThread "http-nio-8080-exec-2"       daemon [_thread_blocked, id=4492, stack(0x0000003e80900000,0x0000003e80a00000) (1024K)]
  0x00000158cae41650 JavaThread "http-nio-8080-exec-3"       daemon [_thread_blocked, id=15540, stack(0x0000003e80a00000,0x0000003e80b00000) (1024K)]
  0x00000158cae3f910 JavaThread "http-nio-8080-exec-4"       daemon [_thread_blocked, id=15460, stack(0x0000003e80b00000,0x0000003e80c00000) (1024K)]
  0x00000158cae424f0 JavaThread "http-nio-8080-exec-5"       daemon [_thread_blocked, id=27192, stack(0x0000003e80c00000,0x0000003e80d00000) (1024K)]
  0x00000158ca45a5b0 JavaThread "http-nio-8080-exec-6"       daemon [_thread_blocked, id=17496, stack(0x0000003e80d00000,0x0000003e80e00000) (1024K)]
  0x00000158ca458870 JavaThread "http-nio-8080-exec-7"       daemon [_thread_blocked, id=23628, stack(0x0000003e80e00000,0x0000003e80f00000) (1024K)]
  0x00000158cb77a510 JavaThread "http-nio-8080-exec-8"       daemon [_thread_blocked, id=8776, stack(0x0000003e80f00000,0x0000003e81000000) (1024K)]
  0x00000158cb7771e0 JavaThread "http-nio-8080-exec-9"       daemon [_thread_blocked, id=2580, stack(0x0000003e81000000,0x0000003e81100000) (1024K)]
  0x00000158cb778f20 JavaThread "http-nio-8080-exec-10"      daemon [_thread_blocked, id=16044, stack(0x0000003e81100000,0x0000003e81200000) (1024K)]
  0x00000158cb778080 JavaThread "http-nio-8080-Poller"       daemon [_thread_in_native, id=3320, stack(0x0000003e81200000,0x0000003e81300000) (1024K)]
  0x00000158cb779dc0 JavaThread "http-nio-8080-Acceptor"     daemon [_thread_in_native, id=22512, stack(0x0000003e81300000,0x0000003e81400000) (1024K)]
  0x00000158cb77ac60 JavaThread "DestroyJavaVM"                     [_thread_blocked, id=24548, stack(0x0000003efd400000,0x0000003efd500000) (1024K)]
  0x00000158c117dc90 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=2628, stack(0x0000003e81500000,0x0000003e81600000) (1024K)]
  0x00000158cb435710 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=12700, stack(0x0000003e81600000,0x0000003e81700000) (1024K)]
  0x00000158cb439b20 JavaThread "C1 CompilerThread3"         daemon [_thread_blocked, id=15812, stack(0x0000003e81700000,0x0000003e81800000) (1024K)]
Total: 35

Other Threads:
  0x00000158e667afb0 VMThread "VM Thread"                           [id=18172, stack(0x0000003efdb00000,0x0000003efdc00000) (1024K)]
  0x00000158c111cd80 WatcherThread "VM Periodic Task Thread"        [id=29524, stack(0x0000003efda00000,0x0000003efdb00000) (1024K)]
  0x00000158e6566700 WorkerThread "GC Thread#0"                     [id=10496, stack(0x0000003efd500000,0x0000003efd600000) (1024K)]
  0x00000158c6d21d20 WorkerThread "GC Thread#1"                     [id=20436, stack(0x0000003efec00000,0x0000003efed00000) (1024K)]
  0x00000158c6d22480 WorkerThread "GC Thread#2"                     [id=30112, stack(0x0000003efed00000,0x0000003efee00000) (1024K)]
  0x00000158c6d22be0 WorkerThread "GC Thread#3"                     [id=19632, stack(0x0000003efee00000,0x0000003efef00000) (1024K)]
  0x00000158c6d22f90 WorkerThread "GC Thread#4"                     [id=27268, stack(0x0000003efef00000,0x0000003eff000000) (1024K)]
  0x00000158c6d220d0 WorkerThread "GC Thread#5"                     [id=5484, stack(0x0000003eff000000,0x0000003eff100000) (1024K)]
  0x00000158c6d23340 WorkerThread "GC Thread#6"                     [id=21048, stack(0x0000003eff100000,0x0000003eff200000) (1024K)]
  0x00000158c6e8cfc0 WorkerThread "GC Thread#7"                     [id=25116, stack(0x0000003eff200000,0x0000003eff300000) (1024K)]
  0x00000158c6e8b240 WorkerThread "GC Thread#8"                     [id=28532, stack(0x0000003eff300000,0x0000003eff400000) (1024K)]
  0x00000158c6e8dad0 WorkerThread "GC Thread#9"                     [id=30260, stack(0x0000003eff400000,0x0000003eff500000) (1024K)]
  0x00000158c6e8de80 WorkerThread "GC Thread#10"                    [id=448, stack(0x0000003eff500000,0x0000003eff600000) (1024K)]
  0x00000158c6e8d370 WorkerThread "GC Thread#11"                    [id=27240, stack(0x0000003eff600000,0x0000003eff700000) (1024K)]
  0x00000158c6e8c4b0 WorkerThread "GC Thread#12"                    [id=7716, stack(0x0000003eff700000,0x0000003eff800000) (1024K)]
  0x00000158e65b5010 ConcurrentGCThread "G1 Main Marker"            [id=17728, stack(0x0000003efd600000,0x0000003efd700000) (1024K)]
  0x00000158e65b7480 WorkerThread "G1 Conc#0"                       [id=29312, stack(0x0000003efd700000,0x0000003efd800000) (1024K)]
  0x00000158c6e8e230 WorkerThread "G1 Conc#1"                       [id=28372, stack(0x0000003effb00000,0x0000003effc00000) (1024K)]
  0x00000158c6e8c860 WorkerThread "G1 Conc#2"                       [id=7416, stack(0x0000003effc00000,0x0000003effd00000) (1024K)]
  0x00000158e663a1c0 ConcurrentGCThread "G1 Refine#0"               [id=15288, stack(0x0000003efd800000,0x0000003efd900000) (1024K)]
  0x00000158e663aaf0 ConcurrentGCThread "G1 Service"                [id=9720, stack(0x0000003efd900000,0x0000003efda00000) (1024K)]
Total: 21

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000704a00000, size: 4022 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000015880000000-0x0000015880d40000-0x0000015880d40000), size 13893632, SharedBaseAddress: 0x0000015880000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000015881000000-0x00000158c1000000, reserved size: 1073741824
UseCompressedClassPointers 1, UseCompactObjectHeaders 0
Narrow klass pointer bits 32, Max shift 3
Narrow klass base: 0x0000015880000000, Narrow klass shift: 0
Encoding Range: [0x0000015880000000 - 0x0000015980000000), (4294967296 bytes)
Klass Range:    [0x0000015880000000 - 0x00000158c1000000), (1090519040 bytes)
Klass ID Range:  [8 - 1090519033) (1090519025)

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 16 total, 16 available
 Memory: 16085M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 4022M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total reserved 4118528K, committed 81920K, used 36611K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 2 survivors (4096K)
 Metaspace       used 74177K, committed 74752K, reserved 1179648K
  class space    used 8050K, committed 8256K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000704a00000, 0x0000000704c00000, 0x0000000704c00000|100%| O|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked |  0
|   1|0x0000000704c00000, 0x0000000704de9460, 0x0000000704e00000| 95%| O|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked |  0
|   2|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked |  0
|   3|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked |  0
|   4|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked |  0
|   5|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked |  0
|   6|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked |  0
|   7|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked |  0
|   8|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked |  0
|   9|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked |  0
|  10|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked |  0
|  11|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked |  0
|  12|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked |  0
|  13|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked |  0
|  14|0x0000000706600000, 0x000000070676f6d0, 0x0000000706800000| 71%| O|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked |  0
|  15|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked |  0
|  16|0x0000000706a00000, 0x0000000706a68240, 0x0000000706c00000| 20%| S|CS|TAMS 0x0000000706a00000| PB 0x0000000706a00000| Complete |  0
|  17|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| S|CS|TAMS 0x0000000706c00000| PB 0x0000000706c00000| Complete |  0
|  18|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked |  0
|  19|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked |  0
|  20|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked |  0
|  21|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked |  0
|  22|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked |  0
|  23|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked |  0
|  24|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked |  0
|  25|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked |  0
|  26|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked |  0
|  27|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked |  0
|  28|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked |  0
|  29|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked |  0
|  30|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked |  0
|  31|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked |  0
|  32|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked |  0
| 105|0x0000000711c00000, 0x0000000711c00000, 0x0000000711e00000|  0%| F|  |TAMS 0x0000000711c00000| PB 0x0000000711c00000| Untracked |  0
| 106|0x0000000711e00000, 0x0000000711e00000, 0x0000000712000000|  0%| F|  |TAMS 0x0000000711e00000| PB 0x0000000711e00000| Untracked |  0
| 107|0x0000000712000000, 0x0000000712000000, 0x0000000712200000|  0%| F|  |TAMS 0x0000000712000000| PB 0x0000000712000000| Untracked |  0
| 108|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| E|  |TAMS 0x0000000712200000| PB 0x0000000712200000| Complete |  0
| 109|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| E|CS|TAMS 0x0000000712400000| PB 0x0000000712400000| Complete |  0
| 125|0x0000000714400000, 0x0000000714600000, 0x0000000714600000|100%| E|  |TAMS 0x0000000714400000| PB 0x0000000714400000| Complete |  0
|2010|0x00000007ffe00000, 0x0000000800000000, 0x0000000800000000|100%| E|CS|TAMS 0x00000007ffe00000| PB 0x00000007ffe00000| Complete |  0

Card table byte_map: [0x00000158f3d90000,0x00000158f4570000] _byte_map_base: 0x00000158f056b000

Marking Bits: (CMBitMap*) 0x00000158e6566d20
 Bits: [0x00000158f4570000, 0x00000158f8448000)

Polling page: 0x00000158e4780000

Metaspace:

Usage:
  Non-class:     64.58 MB used.
      Class:      7.86 MB used.
       Both:     72.44 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      64.94 MB ( 51%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       8.06 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      73.00 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  14.86 MB
       Class:  7.76 MB
        Both:  22.62 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 98.25 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
UseCompressedClassPointers 1, UseCompactObjectHeaders 0
Narrow klass pointer bits 32, Max shift 3
Narrow klass base: 0x0000015880000000, Narrow klass shift: 0
Encoding Range: [0x0000015880000000 - 0x0000015980000000), (4294967296 bytes)
Klass Range:    [0x0000015880000000 - 0x00000158c1000000), (1090519040 bytes)
Klass ID Range:  [8 - 1090519033) (1090519025)


Internal statistics:

num_allocs_failed_limit: 11.
num_arena_births: 772.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1168.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 11.
num_chunks_taken_from_freelist: 3104.
num_chunk_merges: 11.
num_chunk_splits: 2267.
num_chunks_enlarged: 1836.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=11191Kb max_used=12139Kb free=37960Kb
 bounds [0x00000158efa00000, 0x00000158f05e0000, 0x00000158f2a00000]
 total_blobs=7157, nmethods=6392, adapters=688, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 126.727 Thread 0x00000158c117dc90 nmethod 6456 0x00000158efcd0608 code [0x00000158efcd0720, 0x00000158efcd0850]
Event: 126.727 Thread 0x00000158cb439b20 nmethod 6457 0x00000158efcd0308 code [0x00000158efcd0420, 0x00000158efcd05a0]
Event: 126.727 Thread 0x00000158c1130370 nmethod 6458 0x00000158efcd0008 code [0x00000158efcd0120, 0x00000158efcd0250]
Event: 126.727 Thread 0x00000158cb435710 nmethod 6459 0x00000158efccfd08 code [0x00000158efccfe20, 0x00000158efccff48]
Event: 126.729 Thread 0x00000158cb439b20 6460       1       java.time.ZoneOffset::getTotalSeconds (5 bytes)
Event: 126.729 Thread 0x00000158cb439b20 nmethod 6460 0x00000158efccfa08 code [0x00000158efccfb00, 0x00000158efccfbc8]
Event: 126.729 Thread 0x00000158c117dc90 6461       1       ch.qos.logback.classic.spi.StackTraceElementProxy::toString (5 bytes)
Event: 126.729 Thread 0x00000158c1130370 6462       1       ch.qos.logback.classic.spi.StackTraceElementProxy::getSTEAsString (28 bytes)
Event: 126.729 Thread 0x00000158cb439b20 6463       1       ch.qos.logback.classic.spi.StackTraceElementProxy::getClassPackagingData (5 bytes)
Event: 126.729 Thread 0x00000158cb435710 6464       1       ch.qos.logback.classic.spi.ClassPackagingData::isExact (5 bytes)
Event: 126.729 Thread 0x00000158cb435710 nmethod 6464 0x00000158efccf708 code [0x00000158efccf800, 0x00000158efccf8c8]
Event: 126.729 Thread 0x00000158cb439b20 nmethod 6463 0x00000158efccf408 code [0x00000158efccf500, 0x00000158efccf5c8]
Event: 126.729 Thread 0x00000158cb435710 6465       1       ch.qos.logback.classic.spi.ClassPackagingData::getCodeLocation (5 bytes)
Event: 126.729 Thread 0x00000158cb439b20 6466       1       ch.qos.logback.classic.spi.ClassPackagingData::getVersion (5 bytes)
Event: 126.729 Thread 0x00000158cb435710 nmethod 6465 0x00000158efccf108 code [0x00000158efccf200, 0x00000158efccf2c8]
Event: 126.729 Thread 0x00000158cb439b20 nmethod 6466 0x00000158efccee08 code [0x00000158efccef00, 0x00000158efccefc8]
Event: 126.729 Thread 0x00000158cb435710 6467       1       org.apache.catalina.connector.Response::getCoyoteResponse (5 bytes)
Event: 126.729 Thread 0x00000158cb435710 nmethod 6467 0x00000158efcceb08 code [0x00000158efccec00, 0x00000158efccecc8]
Event: 126.730 Thread 0x00000158c1130370 nmethod 6462 0x00000158efcce308 code [0x00000158efcce4a0, 0x00000158efcce9d8]
Event: 126.730 Thread 0x00000158c117dc90 nmethod 6461 0x00000158efccd988 code [0x00000158efccdb60, 0x00000158efcce238]

GC Heap History (20 events):
Event: 1.676 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 44053K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 19 young (38912K), 6 survivors (12288K)
 Metaspace       used 29218K, committed 29568K, reserved 1114112K
  class space    used 3013K, committed 3136K, reserved 1048576K
}
Event: 1.683 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 17969K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 29218K, committed 29568K, reserved 1114112K
  class space    used 3013K, committed 3136K, reserved 1048576K
}
Event: 2.003 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 48689K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 1 survivors (2048K)
 Metaspace       used 33533K, committed 33920K, reserved 1114112K
  class space    used 3559K, committed 3712K, reserved 1048576K
}
Event: 2.008 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 20017K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 33533K, committed 33920K, reserved 1114112K
  class space    used 3559K, committed 3712K, reserved 1048576K
}
Event: 2.154 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 34353K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 2 survivors (4096K)
 Metaspace       used 35689K, committed 36096K, reserved 1114112K
  class space    used 3837K, committed 4032K, reserved 1048576K
}
Event: 2.157 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 21474K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 35689K, committed 36096K, reserved 1114112K
  class space    used 3837K, committed 4032K, reserved 1048576K
}
Event: 2.485 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 52194K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 2 survivors (4096K)
 Metaspace       used 42418K, committed 42816K, reserved 1114112K
  class space    used 4555K, committed 4736K, reserved 1048576K
}
Event: 2.488 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 22992K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 42418K, committed 42816K, reserved 1114112K
  class space    used 4555K, committed 4736K, reserved 1048576K
}
Event: 2.949 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 53712K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 2 survivors (4096K)
 Metaspace       used 52345K, committed 52800K, reserved 1114112K
  class space    used 5553K, committed 5760K, reserved 1048576K
}
Event: 2.952 GC heap after
{Heap after GC invocations=10 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 25281K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 52345K, committed 52800K, reserved 1114112K
  class space    used 5553K, committed 5760K, reserved 1048576K
}
Event: 3.270 GC heap before
{Heap before GC invocations=10 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 51905K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 3 survivors (6144K)
 Metaspace       used 59450K, committed 59904K, reserved 1114112K
  class space    used 6253K, committed 6400K, reserved 1048576K
}
Event: 3.274 GC heap after
{Heap after GC invocations=11 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 26993K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 59450K, committed 59904K, reserved 1114112K
  class space    used 6253K, committed 6400K, reserved 1048576K
}
Event: 3.293 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 26993K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 3 survivors (6144K)
 Metaspace       used 59841K, committed 60288K, reserved 1114112K
  class space    used 6332K, committed 6528K, reserved 1048576K
}
Event: 3.296 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 27055K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 59841K, committed 60288K, reserved 1114112K
  class space    used 6332K, committed 6528K, reserved 1048576K
}
Event: 3.674 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 57775K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 1 survivors (2048K)
 Metaspace       used 65143K, committed 65664K, reserved 1114112K
  class space    used 7011K, committed 7232K, reserved 1048576K
}
Event: 3.676 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 28524K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 65143K, committed 65664K, reserved 1114112K
  class space    used 7011K, committed 7232K, reserved 1048576K
}
Event: 3.918 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 55148K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 2 survivors (4096K)
 Metaspace       used 68162K, committed 68736K, reserved 1114112K
  class space    used 7378K, committed 7616K, reserved 1048576K
}
Event: 3.921 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 30208K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 68162K, committed 68736K, reserved 1114112K
  class space    used 7378K, committed 7616K, reserved 1048576K
}
Event: 8.287 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total reserved 4118528K, committed 67584K, used 54784K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 2 survivors (4096K)
 Metaspace       used 72636K, committed 73152K, reserved 1114112K
  class space    used 7867K, committed 8064K, reserved 1048576K
}
Event: 8.291 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total reserved 4118528K, committed 81920K, used 32515K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 72636K, committed 73152K, reserved 1114112K
  class space    used 7867K, committed 8064K, reserved 1048576K
}

Dll operation events (13 events):
Event: 0.047 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\java.dll
Event: 0.048 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\zip.dll
Event: 0.176 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\instrument.dll
Event: 0.214 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\jimage.dll
Event: 0.227 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\net.dll
Event: 0.228 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\nio.dll
Event: 0.238 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\zip.dll
Event: 0.330 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\management.dll
Event: 0.341 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\management_ext.dll
Event: 0.552 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\extnet.dll
Event: 1.315 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\sunmscapi.dll
Event: 2.367 Loaded shared library C:\Users\<USER>\.jdks\openjdk-24\bin\verify.dll
Event: 126.736 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder\libasyncProfiler.dll

Deoptimization events (20 events):
Event: 8.263 Thread 0x00000158ca45a5b0 DEOPT PACKING pc=0x00000158efe80951 sp=0x0000003e80dfc580
Event: 8.263 Thread 0x00000158ca45a5b0 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80dfba28 mode 1
Event: 8.263 Thread 0x00000158ca45a5b0 DEOPT PACKING pc=0x00000158efe8058c sp=0x0000003e80dfc5e0
Event: 8.263 Thread 0x00000158ca45a5b0 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80dfba98 mode 1
Event: 8.263 Thread 0x00000158ca45a5b0 DEOPT PACKING pc=0x00000158efe80951 sp=0x0000003e80dfc640
Event: 8.263 Thread 0x00000158ca45a5b0 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80dfbae8 mode 1
Event: 8.263 Thread 0x00000158ca45a5b0 DEOPT PACKING pc=0x00000158efe8058c sp=0x0000003e80dfc6a0
Event: 8.263 Thread 0x00000158ca45a5b0 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80dfbb58 mode 1
Event: 25.766 Thread 0x00000158ca45a5b0 DEOPT PACKING pc=0x00000158f02ac13c sp=0x0000003e80dfd3e0
Event: 25.766 Thread 0x00000158ca45a5b0 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80dfc8d0 mode 1
Event: 25.769 Thread 0x00000158ca45a5b0 DEOPT PACKING pc=0x00000158f02a8d6c sp=0x0000003e80dfd570
Event: 25.769 Thread 0x00000158ca45a5b0 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80dfca60 mode 1
Event: 60.334 Thread 0x00000158cae3f910 DEOPT PACKING pc=0x00000158f02ac13c sp=0x0000003e80bfd450
Event: 60.334 Thread 0x00000158cae3f910 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80bfc940 mode 1
Event: 60.334 Thread 0x00000158cae3f910 DEOPT PACKING pc=0x00000158f02a8d6c sp=0x0000003e80bfd5e0
Event: 60.334 Thread 0x00000158cae3f910 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80bfcad0 mode 1
Event: 72.856 Thread 0x00000158cae41650 DEOPT PACKING pc=0x00000158f02ac13c sp=0x0000003e80afdad0
Event: 72.856 Thread 0x00000158cae41650 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80afcfc0 mode 1
Event: 72.857 Thread 0x00000158cae41650 DEOPT PACKING pc=0x00000158f02a8d6c sp=0x0000003e80afdc60
Event: 72.857 Thread 0x00000158cae41650 DEOPT UNPACKING pc=0x00000158efa56642 sp=0x0000003e80afd150 mode 1

Classes loaded (20 events):
Event: 44.686 Loading class com/fasterxml/jackson/databind/ser/std/NumberSerializers$1
Event: 44.686 Loading class com/fasterxml/jackson/databind/ser/std/NumberSerializers$1 done
Event: 44.686 Loading class com/fasterxml/jackson/databind/ser/impl/PropertySerializerMap$Multi
Event: 44.686 Loading class com/fasterxml/jackson/databind/ser/impl/PropertySerializerMap$Multi done
Event: 44.686 Loading class com/fasterxml/jackson/core/io/NumberOutput
Event: 44.686 Loading class com/fasterxml/jackson/core/io/NumberOutput done
Event: 44.687 Loading class com/fasterxml/jackson/databind/ser/impl/PropertySerializerMap$TypeAndSerializer
Event: 44.687 Loading class com/fasterxml/jackson/databind/ser/impl/PropertySerializerMap$TypeAndSerializer done
Event: 44.687 Loading class org/apache/coyote/http11/Constants
Event: 44.687 Loading class org/apache/coyote/http11/Constants done
Event: 44.687 Loading class org/apache/tomcat/util/buf/HexUtils
Event: 44.687 Loading class org/apache/tomcat/util/buf/HexUtils done
Event: 44.688 Loading class org/springframework/web/method/annotation/SessionAttributesHandler
Event: 44.688 Loading class org/springframework/web/method/annotation/SessionAttributesHandler done
Event: 44.688 Loading class org/springframework/web/context/request/WebRequest
Event: 44.688 Loading class org/springframework/web/context/request/WebRequest done
Event: 60.334 Loading class org/springframework/web/method/annotation/ExceptionHandlerMethodResolver$ExceptionMapping
Event: 60.334 Loading class org/springframework/web/method/annotation/ExceptionHandlerMethodResolver$ExceptionMapping done
Event: 126.717 Loading class kotlinx/coroutines/debug/internal/DebugProbesImpl
Event: 126.717 Loading class kotlinx/coroutines/debug/internal/DebugProbesImpl done

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 0.198 Thread 0x00000158e667afb0 redefined class name=java.lang.Throwable, count=1

Internal exceptions (20 events):
Event: 3.718 Thread 0x00000158e64ef790 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000071247b420}: Found class java.lang.Object, but interface was expected> (0x000000071247b420) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 872]
Event: 3.769 Thread 0x00000158e64ef790 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000711ffa488}: Found class java.lang.Object, but interface was expected> (0x0000000711ffa488) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 872]
Event: 3.808 Thread 0x00000158e64ef790 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707dad068}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoker(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000707dad068) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 3.834 Thread 0x00000158e64ef790 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707bf23a0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000707bf23a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 3.851 Thread 0x00000158e64ef790 Exception <a 'java/io/IOException'{0x00000007079bee58}> (0x00000007079bee58) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 518]
Event: 3.939 Thread 0x00000158e64ef790 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000007fffdbfe0}: Found class java.lang.Object, but interface was expected> (0x00000007fffdbfe0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 872]
Event: 3.980 Thread 0x00000158e64ef790 Exception <a 'java/lang/NoSuchMethodError'{0x00000007125fbd28}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007125fbd28) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 4.011 Thread 0x00000158e64ef790 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000007120c2f70}: Found class java.lang.Object, but interface was expected> (0x00000007120c2f70) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 872]
Event: 4.068 Thread 0x00000158e64ef790 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711dd5770}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000711dd5770) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 4.323 Thread 0x00000158c8fee120 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707b93de0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000707b93de0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 8.169 Thread 0x00000158cb779dc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707829ff8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int, int)'> (0x0000000707829ff8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 8.169 Thread 0x00000158cb779dc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707832050}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, int, int)'> (0x0000000707832050) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 8.188 Thread 0x00000158ca45a5b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007079603c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000007079603c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 8.189 Thread 0x00000158ca45a5b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000707968e28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000707968e28) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 8.189 Thread 0x00000158ca45a5b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007079711c0}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000007079711c0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 8.189 Thread 0x00000158ca45a5b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007079815f0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000007079815f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 805]
Event: 25.766 Thread 0x00000158ca45a5b0 Exception <a 'java/lang/NullPointerException'{0x00000007145696f0}> (0x00000007145696f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 1417]
Event: 26.806 Thread 0x00000158ca45a5b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000071456c820}: Found class java.lang.Object, but interface was expected> (0x000000071456c820) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 872]
Event: 60.334 Thread 0x00000158cae3f910 Exception <a 'java/lang/NullPointerException'{0x00000007122d3480}> (0x00000007122d3480) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 1417]
Event: 72.856 Thread 0x00000158cae41650 Exception <a 'java/lang/NullPointerException'{0x0000000712319be8}> (0x0000000712319be8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 1417]

VM Operations (20 events):
Event: 78.447 Executing VM operation: get/set locals
Event: 78.447 Executing VM operation: get/set locals done
Event: 78.447 Executing VM operation: get/set locals
Event: 78.447 Executing VM operation: get/set locals done
Event: 78.447 Executing VM operation: get/set locals
Event: 78.447 Executing VM operation: get/set locals done
Event: 78.447 Executing VM operation: get/set locals
Event: 78.447 Executing VM operation: get/set locals done
Event: 78.447 Executing VM operation: get/set locals
Event: 78.447 Executing VM operation: get/set locals done
Event: 78.447 Executing VM operation: get/set locals
Event: 78.447 Executing VM operation: get/set locals done
Event: 78.447 Executing VM operation: get/set locals
Event: 78.447 Executing VM operation: get/set locals done
Event: 78.447 Executing VM operation: get/set locals
Event: 78.447 Executing VM operation: get/set locals done
Event: 78.448 Executing VM operation: get/set locals
Event: 78.448 Executing VM operation: get/set locals done
Event: 91.018 Executing VM operation: get/set locals
Event: 91.018 Executing VM operation: get/set locals done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f0212188
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f023b308
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158efe32808
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f0190208
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158efedbc88
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f01d5f08
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f01d7808
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158efd4be08
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158effb7888
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f01a6488
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f0223888
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f036e788
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158efeebd88
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158effcad08
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f0195108
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f0213d08
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f02c2008
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f02c2608
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f02c2f08
Event: 3.305 Thread 0x00000158e667afb0 flushing  nmethod 0x00000158f02f0008

Events (20 events):
Event: 4.083 Thread 0x00000158e64ef790 Thread added: 0x00000158cb779dc0
Event: 4.094 Thread 0x00000158e64ef790 Thread exited: 0x00000158e64ef790
Event: 4.094 Thread 0x00000158cb77ac60 Thread added: 0x00000158cb77ac60
Event: 4.317 Thread 0x00000158c8fee120 Thread added: 0x00000158cb779670
Event: 4.321 Thread 0x00000158cb779670 Thread exited: 0x00000158cb779670
Event: 4.321 Thread 0x00000158c8fee120 Thread added: 0x00000158cb779670
Event: 4.325 Thread 0x00000158cb779670 Thread exited: 0x00000158cb779670
Event: 4.325 Thread 0x00000158c8fee120 Thread added: 0x00000158cb777930
Event: 4.328 Thread 0x00000158cb777930 Thread exited: 0x00000158cb777930
Event: 8.173 Thread 0x00000158c692cf80 Thread exited: 0x00000158c692cf80
Event: 8.173 Thread 0x00000158c6929300 Thread exited: 0x00000158c6929300
Event: 8.179 Thread 0x00000158c117c5e0 Thread exited: 0x00000158c117c5e0
Event: 8.277 Thread 0x00000158c9d2a8a0 Thread exited: 0x00000158c9d2a8a0
Event: 13.690 Thread 0x00000158c117be50 Thread exited: 0x00000158c117be50
Event: 18.690 Thread 0x00000158c117dc90 Thread exited: 0x00000158c117dc90
Event: 126.726 Thread 0x00000158c1130370 Thread added: 0x00000158c117dc90
Event: 126.726 Thread 0x00000158c1130370 Thread added: 0x00000158cb435710
Event: 126.726 Thread 0x00000158c1130370 Thread added: 0x00000158cb439b20
Event: 126.728 Thread 0x00000158c8fee120 Thread exited: 0x00000158c8fee120
Event: 126.728 Thread 0x00000158c7693480 Thread exited: 0x00000158c7693480


Dynamic libraries:
0x00007ff6fb800000 - 0x00007ff6fb80e000 	C:\Users\<USER>\.jdks\openjdk-24\bin\java.exe
0x00007ffcf3160000 - 0x00007ffcf33c8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffcf23d0000 - 0x00007ffcf2499000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffcf0650000 - 0x00007ffcf0a3d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffcf0bc0000 - 0x00007ffcf0d0b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffcdebe0000 - 0x00007ffcdebf6000 	C:\Users\<USER>\.jdks\openjdk-24\bin\jli.dll
0x00007ffcdd140000 - 0x00007ffcdd15b000 	C:\Users\<USER>\.jdks\openjdk-24\bin\VCRUNTIME140.dll
0x00007ffcf1540000 - 0x00007ffcf170c000 	C:\WINDOWS\System32\USER32.dll
0x00007ffcef4a0000 - 0x00007ffcef73a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ffcf02b0000 - 0x00007ffcf02d7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffcf1390000 - 0x00007ffcf1439000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffcf2380000 - 0x00007ffcf23ab000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffcf0460000 - 0x00007ffcf0597000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffcf05a0000 - 0x00007ffcf0643000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffcf1f20000 - 0x00007ffcf1f4f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffcdebd0000 - 0x00007ffcdebdc000 	C:\Users\<USER>\.jdks\openjdk-24\bin\vcruntime140_1.dll
0x00007ffcdc690000 - 0x00007ffcdc71e000 	C:\Users\<USER>\.jdks\openjdk-24\bin\msvcp140.dll
0x00007ffc262d0000 - 0x00007ffc27072000 	C:\Users\<USER>\.jdks\openjdk-24\bin\server\jvm.dll
0x00007ffcf1710000 - 0x00007ffcf17c4000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffcf24a0000 - 0x00007ffcf2546000 	C:\WINDOWS\System32\sechost.dll
0x00007ffcf2df0000 - 0x00007ffcf2f08000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffcf27b0000 - 0x00007ffcf2824000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffcf0100000 - 0x00007ffcf015e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffcda9b0000 - 0x00007ffcda9e5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffce4c00000 - 0x00007ffce4c0b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffcf00e0000 - 0x00007ffcf00f4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffcee6f0000 - 0x00007ffcee70b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffcdeba0000 - 0x00007ffcdebaa000 	C:\Users\<USER>\.jdks\openjdk-24\bin\jimage.dll
0x00007ffcefa60000 - 0x00007ffcefca1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffcf1ff0000 - 0x00007ffcf2376000 	C:\WINDOWS\System32\combase.dll
0x00007ffcf12a0000 - 0x00007ffcf1380000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffcef940000 - 0x00007ffcef983000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffcf0d10000 - 0x00007ffcf0da9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffc9d890000 - 0x00007ffc9d8cc000 	C:\Users\<USER>\.jdks\openjdk-24\bin\jdwp.dll
0x00007ffc125c0000 - 0x00007ffc127ce000 	C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder\libasyncProfiler.dll
0x00007ffcded50000 - 0x00007ffcded5f000 	C:\Users\<USER>\.jdks\openjdk-24\bin\instrument.dll
0x00007ffcdd120000 - 0x00007ffcdd13e000 	C:\Users\<USER>\.jdks\openjdk-24\bin\java.dll
0x00007ffcf10f0000 - 0x00007ffcf128e000 	C:\WINDOWS\System32\ole32.dll
0x00007ffcf17d0000 - 0x00007ffcf1f1a000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffcf02e0000 - 0x00007ffcf0454000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffcbca00000 - 0x00007ffcbca17000 	C:\Users\<USER>\.jdks\openjdk-24\bin\zip.dll
0x00007ffced440000 - 0x00007ffcedc9b000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffcf2f60000 - 0x00007ffcf3055000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffcf1080000 - 0x00007ffcf10ea000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffcf01c0000 - 0x00007ffcf01ef000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffcd32f0000 - 0x00007ffcd32fc000 	C:\Users\<USER>\.jdks\openjdk-24\bin\dt_socket.dll
0x00007ffcee130000 - 0x00007ffcee163000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffceeca0000 - 0x00007ffceed0a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffcdd110000 - 0x00007ffcdd120000 	C:\Users\<USER>\.jdks\openjdk-24\bin\net.dll
0x00007ffcdc170000 - 0x00007ffcdc186000 	C:\Users\<USER>\.jdks\openjdk-24\bin\nio.dll
0x00007ffcee1c0000 - 0x00007ffcee2e6000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffcf3110000 - 0x00007ffcf311a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffce8f20000 - 0x00007ffce8f2b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffce11b0000 - 0x00007ffce1236000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffcd02b0000 - 0x00007ffcd02ba000 	C:\Users\<USER>\.jdks\openjdk-24\bin\management.dll
0x00007ffcc26b0000 - 0x00007ffcc26bb000 	C:\Users\<USER>\.jdks\openjdk-24\bin\management_ext.dll
0x00007ffcf28f0000 - 0x00007ffcf28f8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffcefdd0000 - 0x00007ffcefdeb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffceebc0000 - 0x00007ffceebfb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffceed70000 - 0x00007ffceed9b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffcf0190000 - 0x00007ffcf01b6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffcef120000 - 0x00007ffcef12c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffcbf230000 - 0x00007ffcbf248000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffcbf1e0000 - 0x00007ffcbf1f2000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffcbf1a0000 - 0x00007ffcbf1d0000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffcbe9a0000 - 0x00007ffcbe9c0000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffcd9ea0000 - 0x00007ffcd9ea9000 	C:\Users\<USER>\.jdks\openjdk-24\bin\extnet.dll
0x00007ffcd2ce0000 - 0x00007ffcd2cee000 	C:\Users\<USER>\.jdks\openjdk-24\bin\sunmscapi.dll
0x00007ffcf0a40000 - 0x00007ffcf0bb7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffcef1f0000 - 0x00007ffcef220000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffcef1a0000 - 0x00007ffcef1df000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffcd6ef0000 - 0x00007ffcd6f00000 	C:\Users\<USER>\.jdks\openjdk-24\bin\verify.dll
0x00007ffc861c0000 - 0x00007ffc861c8000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\openjdk-24\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;C:\Users\<USER>\.jdks\openjdk-24\bin\server;C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:57776,suspend=y,server=n -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\WebProjectApplication_2025_07_18_222324.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\WebProjectApplication_2025_07_18_222324.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 
java_command: com.example.web_project.WebProjectApplication
java_class_path (initial): D:\GA\download\BTL-LTW\web_project\web_project\target\classes;D:\GA\M2\org\springframework\boot\spring-boot-starter-data-jpa\3.5.3\spring-boot-starter-data-jpa-3.5.3.jar;D:\GA\M2\org\springframework\boot\spring-boot-starter\3.5.3\spring-boot-starter-3.5.3.jar;D:\GA\M2\org\springframework\boot\spring-boot\3.5.3\spring-boot-3.5.3.jar;D:\GA\M2\org\springframework\boot\spring-boot-autoconfigure\3.5.3\spring-boot-autoconfigure-3.5.3.jar;D:\GA\M2\org\springframework\boot\spring-boot-starter-logging\3.5.3\spring-boot-starter-logging-3.5.3.jar;D:\GA\M2\ch\qos\logback\logback-classic\1.5.18\logback-classic-1.5.18.jar;D:\GA\M2\ch\qos\logback\logback-core\1.5.18\logback-core-1.5.18.jar;D:\GA\M2\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;D:\GA\M2\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;D:\GA\M2\org\slf4j\jul-to-slf4j\2.0.17\jul-to-slf4j-2.0.17.jar;D:\GA\M2\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\GA\M2\org\yaml\snakeyaml\2.4\snakeyaml-2.4.jar;D:\GA\M2\org\springframework\boot\spring-boot-starter-jdbc\3.5.3\spring-boot-starter-jdbc-3.5.3.jar;D:\GA\M2\com\zaxxer\HikariCP\6.3.0\HikariCP-6.3.0.jar;D:\GA\M2\org\springframework\spring-jdbc\6.2.8\spring-jdbc-6.2.8.jar;D:\GA\M2\org\hibernate\orm\hibernate-core\6.6.18.Final\hibernate-core-6.6.18.Final.jar;D:\GA\M2\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;D:\GA\M2\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;D:\GA\M2\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;D:\GA\M2\org\hibernate\common\hibernate-commons-annotations\7.0.3.Final\hibernate-commons-annotations-7.0.3.Final.jar;D:\GA\M2\io\smallrye\jandex\3.2.0\jandex-3.2.0.jar;D:\GA\M2\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;D:\GA\M2\net\bytebuddy\byte-buddy\1.17.6\byte-buddy-1.17.6.jar;D:\GA\M2\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;D:\GA\M2\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;D:\GA\M2\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;D:\GA\M2\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;D:\GA\M2\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\GA\M2\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;D:\GA\M2\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;D:\GA\M2\org\springframework\data\spring-data-jpa\3.5.1\spring-data-jpa-3.5.1.jar;D:\GA\M2\org\springframework\data\spring-data-commons\3.5.1\spring-data-commons-3.5.1.jar;D:\GA\M2\org\springframework\spring-orm\6.2.8\spring-orm-6.2.8.jar;D:\GA\M2\org\springframework\spring-context\6.2.8\spring-context-6.2.8.jar;D:\GA\M2\org\springframework\spring-aop\6.2.8\spring-aop-6.2.8.jar;D:\GA\M2\org\springframework\spring-tx\6.2.8\spring-tx-6.2.8.jar;D:\GA\M2\org\springframework\spring-beans\6.2.8\spring-beans-6.2.8.jar;D:\GA\M2\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;D:\GA\M2\org\springframework\spring-aspects\6.2.8\spring-aspects-6.2.8.jar;D:\GA\M2\org\aspectj\aspectjweaver\1.9.24\aspectjweaver-1.9.24.jar;D:\GA\M2\org\springframework\boot\spring-boot-starter-web\3.5.3\spring-boot-starter-web-3.5.3.jar;D:\GA\M2\org\springframework\boot\spring-boot-starter-json\3.5.3\spring-boot-starter-json-3.5.3.jar;D:\GA\M2\com\fasterxml\jackson\core\jackson-databind\2.19.1\jackson-databind-2.19.1.jar;D:\GA\M2\com\fasterxml\jackson\core\jackson-annotations\2.19.1\jackson-annotations-2.19.1.jar;D:\GA\M2\com\fasterxml\jackson\core\jackson-core\2.19.1\jackson-core-2.19.1.jar;D:\GA\M2\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.19.1\jackson-datatype-jdk8-2.19.1.jar;D:\GA\M2\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.19.1\jackson-datatype-jsr310-2.19.1.jar;D:\GA\M2\com\fasterxml\jackson\module\jackson-module-parameter-names\2.19.1\jackson-module-parameter-names-2.19.1.jar;D:\GA\M2\org\springframework\boot\spring-boot-starter-tomcat\3.5.3\spring-boot-starter-tomcat-3.5.3.jar;D:\GA\M2\org\apache\tomcat\embed\tomcat-embed-core\10.1.42\tomcat-embed-core-10.1.42.jar;D:\GA\M2\org\apache\tomcat\embed\tomcat-embed-el\10.1.42\tomcat-embed-el-10.1.42.jar;D:\GA\M2\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.42\tomcat-embed-websocket-10.1.42.jar;D:\GA\M2\org\springframework\spring-web\6.2.8\spring-web-6.2.8.jar;D:\GA\M2\io\micrometer\micrometer-observation\1.15.1\micrometer-observation-1.15.1.jar;D:\GA\M2\io\micrometer\micrometer-commons\1.15.1\micrometer-commons-1.15.1.jar;D:\GA\M2\org\springframework\spring-webmvc\6.2.8\spring-webmvc-6.2.8.jar;D:\GA\M2\org\springframework\spring-expression\6.2.8\spring-expression-6.2.8.jar;D:\GA\M2\com\mysql\mysql-connector-j\9.2.0\mysql-connector-j-9.2.0.jar;D:\GA\M2\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;D:\GA\M2\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\GA\M2\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\GA\M2\org\springframework\spring-core\6.2.8\spring-core-6.2.8.jar;D:\GA\M2\org\springframework\spring-jcl\6.2.8\spring-jcl-6.2.8.jar;D:\GA\M2\org\modelmapper\modelmapper\3.2.2\modelmapper-3.2.2.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3.4\lib\idea_rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MarkStackSizeMax                         = 536870912                                 {product} {ergonomic}
   size_t MaxHeapSize                              = 4217372672                                {product} {ergonomic}
   size_t MaxNewSize                               = 2529165312                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 4096                                   {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4217372672                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=%JAVA_HOME%\bin;C:\Program Files\Java\jdk-17\bin;C:\Python311\Scripts\;C:\Python311\;D:\GA\download\ORACLE\WINDOWS.X64_193000_db_home\bin;C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\Downloads\Programs\jdk-18_windows-x64_bin.exe;C:\ProgramData\chocolatey\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\GA\download\MiKTeX\miktex\bin\x64\;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\apache-maven-3.9.6-bin\apache-maven-3.9.6\bin;C:\Program Files\TortoiseGit\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\Docker\Docker\resources\bin;D:\GA\download\MySQL Shell 9.2\bin\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.3.2\bin;;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\GA\download\IntelliJ IDEA 2023.3.4\bin;;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 1 days 4:08 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x26, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 16 processors :
  Max Mhz: 2304, Current Mhz: 2304, Mhz Limit: 2304

Memory: 4k page, system-wide physical 16085M (2506M free)
TotalPageFile size 35541M (AvailPageFile size 8137M)
current process WorkingSet (physical memory assigned to process): 292M, peak: 292M
current process commit charge ("private bytes"): 364M, peak: 417M

vm_info: OpenJDK 64-Bit Server VM (24+36-3646) for windows-amd64 JRE (24+36-3646), built on 2025-02-06T00:20:43Z with MS VC++ 17.6 (VS2022)

END.
