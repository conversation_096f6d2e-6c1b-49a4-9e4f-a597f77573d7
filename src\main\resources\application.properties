spring.application.name=web_project

# Server configuration
server.port=8081

# Database configuration
spring.datasource.url=***********************************
spring.datasource.username=root
spring.datasource.password=270603
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate configuration
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.ddl-auto=update
spring.jpa.open-in-view=false

# File upload configuration
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=100MB